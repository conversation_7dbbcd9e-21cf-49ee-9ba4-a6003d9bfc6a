from pick import pick
from dotenv import load_dotenv
from db import engine
from models import Base

load_dotenv()
Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    title = 'Please select a dataset to sync: '
    options = ['SOC', 'O*NET', 'NAICS', 'Xwalks']
    option, index = pick(options, title, indicator='👉', default_index=0)
    
    if option == 'SOC':
        from source.soc import sync
        sync()
    elif option == 'O*NET':
        from source.onet import sync
        sync()
    elif option == 'NAICS':
        from source.naics import sync
        sync()
    elif option == 'Xwalks':
        from source.onet_soc_xwalk import sync
        sync() 
