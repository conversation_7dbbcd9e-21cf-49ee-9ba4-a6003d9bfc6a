import pandas as pd
import requests
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db import SessionLocal
from models import OnetSocXwalk, OnetOccupation, SocOccupation

URL = "https://www.onetcenter.org/taxonomy/2019/soc/2019_to_SOC_Crosswalk.csv?fmt=csv"
DOWNLOAD_DIR = "./cache"

def download_crosswalk():
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    local_path = os.path.join(DOWNLOAD_DIR, "onet_soc_2019_to_2018.csv")
    resp = requests.get(URL)
    resp.raise_for_status()
    with open(local_path, "wb") as f:
        f.write(resp.content)
    return local_path

def import_to_postgres(file_path):
    """Import a single CSV/TXT file into Postgres"""
    print(f"Importing {file_path}")

    # Load file into DataFrame
    df = pd.read_csv(file_path, dtype=str)
    print("Columns:", df.columns.tolist())

    onet_col = [c for c in df.columns if "O*NET" in c and "Code" in c][0]
    soc_col = [c for c in df.columns if "2018 SOC" in c and "Code" in c][0]

    session = SessionLocal()
    count = 0
    skipped = 0
    try:
        for _, row in df.iterrows():
            onet = row[onet_col].strip()
            soc = row[soc_col].strip().replace("-", "")

            if not onet or not soc:
                continue

            # Check if onet_code exists in onet_occupations table
            onet_exists = session.query(OnetOccupation).filter(OnetOccupation.onet_code == onet).first()
            if not onet_exists:
                print(f"Warning: ONET code '{onet}' not found in onet_occupations table, skipping")
                skipped += 1
                continue

            # Check if soc_code exists in soc_occupations table
            soc_exists = session.query(SocOccupation).filter(SocOccupation.soc_code == soc).first()
            if not soc_exists:
                print(f"Warning: SOC code '{soc}' not found in soc_occupations table, skipping")
                skipped += 1
                continue

            obj = OnetSocXwalk(
                onet_code=onet,
                soc_code=soc
            )
            # Upsert: merge ensures insert or update
            session.merge(obj)
            count += 1

        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
    print(f"Imported {count} rows into onet_soc_xwalk")
    if skipped > 0:
        print(f"Skipped {skipped} rows due to missing parent records")

def sync():
    file_path = download_crosswalk()
    import_to_postgres(file_path)

    print("Done!")