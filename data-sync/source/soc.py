import pandas as pd
import requests
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db import SessionLocal
from models import SocOccupation

SOC_URL = "https://data.widcenter.org/download/soc2018/soccode.csv"
DOWNLOAD_DIR = "./cache"

def download_soc():
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    print(f"Downloading SOC CSV from {SOC_URL} ...")
    resp = requests.get(SOC_URL)
    resp.raise_for_status()
    path = os.path.join(DOWNLOAD_DIR, "soccode.csv")
    with open(path, "wb") as f:
        f.write(resp.content)
    print(f"Saved SOC CSV to {path}")
    return path

def import_to_postgres(file_path, columns):
    """Import a single CSV/TXT file into Postgres"""
    print(f"Importing SOC data from {file_path}")
    df = pd.read_csv(file_path, dtype=str)
    df = df[columns]  # keep only selected columns
    df = df.fillna("")
    session = SessionLocal()

    try:
        for _, row in df.iterrows():
            obj = SocOccupation(
                soc_code=row[columns[0]],
                title=row[columns[1]],
                description=row[columns[2]]
            )
            # Upsert: merge ensures insert or update
            session.merge(obj)
        
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
    print(f"Imported {len(df)} rows into soc_occupations")

def sync():
    file_path = download_soc()
    import_to_postgres(file_path, ["soccode", "soctitle", "socdesc"])

    print("Done!")