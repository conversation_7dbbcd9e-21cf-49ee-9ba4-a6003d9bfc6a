import pandas as pd
import requests
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db import SessionLocal
from models import NaicsIndustry

NAICS_URL = "https://www.bls.gov/cew/classifications/industry/industry-titles.csv"
DOWNLOAD_DIR = "./cache"

def download_naics():
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    print(f"Downloading NAICS CSV from {NAICS_URL} ...")

    resp = requests.get(NAICS_URL, headers=headers, timeout=30)
    resp.raise_for_status()
    path = os.path.join(DOWNLOAD_DIR, "naics.csv")
    with open(path, "wb") as f:
        f.write(resp.content)
    print(f"Saved NAICS CSV to {path}")
    return path

def import_to_postgres(file_path, columns):
    """Import a single CSV/TXT file into Postgres"""
    print(f"Importing NAICS data from {file_path}")
    df = pd.read_csv(file_path, dtype=str)
    df = df[columns]  # keep only selected columns
    df = df.fillna("")
    session = SessionLocal()

    try:
        for _, row in df.iterrows():
            obj = NaicsIndustry(
                naics_code=row[columns[0]],
                title=row[columns[1]]
            )
            # Upsert: merge ensures insert or update
            session.merge(obj)
        
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
    print(f"Imported {len(df)} rows into naics_industries")

def sync():
    # file_path = download_naics()
    file_path = "./cache/industry-titles.csv"
    import_to_postgres(file_path, ["industry_code", "industry_title"])

    print("Done!")

