import pandas as pd
import requests
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db import SessionLocal
from models import OnetOccupation

ONET_URL = "https://www.onetcenter.org/dl_files/database/db_30_0_text/Occupation%20Data.txt"
DOWNLOAD_DIR = "./cache"

def download_onet():
    """Download the O*NET zip file"""
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    print(f"Downloading O*NET data from {ONET_URL} ...")
    resp = requests.get(ONET_URL)
    resp.raise_for_status()

    file_path = os.path.join(DOWNLOAD_DIR, "onet.txt")
    with open(file_path, "wb") as f:
        f.write(resp.content)
    print(f"Saved to {file_path}")
    return file_path

def import_to_postgres(file_path, columns):
    """Import a single CSV/TXT file into Postgres"""
    print(f"Importing {file_path}")

    # Load file into DataFrame
    df = pd.read_csv(file_path, sep="\t", dtype=str)
    df = df[columns]  # keep only selected columns
    df = df.fillna("")
    session = SessionLocal()

    try:
        for _, row in df.iterrows():
            obj = OnetOccupation(
                onet_code=row[columns[0]],
                title=row[columns[1]],
                description=row[columns[2]]
            )
            # Upsert: merge ensures insert or update
            session.merge(obj)
        
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
    print(f"Imported {len(df)} rows into onet_occupations")

def sync():
    file_path = download_onet()
    import_to_postgres(file_path, ["O*NET-SOC Code", "Title", "Description"])

    print("Done!")