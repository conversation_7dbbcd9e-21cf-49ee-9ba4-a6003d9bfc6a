import zipfile
import pandas as pd
import requests
import os

from models.db import get_db
from models.models import ONET

ONET_URL = "https://www.onetcenter.org/dl_files/database/db_30_0_text/Occupation%20Data.txt"
DOWNLOAD_DIR = "./cache"

def download_onet():
    """Download the O*NET zip file"""
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    print(f"Downloading O*NET data from {ONET_URL} ...")
    resp = requests.get(ONET_URL)
    resp.raise_for_status()

    file_path = os.path.join(DOWNLOAD_DIR, "onet.txt")
    with open(file_path, "wb") as f:
        f.write(resp.content)
    print(f"Saved to {file_path}")
    return file_path

def import_to_postgres(file_path, table_name, columns):
    """Import a single CSV/TXT file into Postgres"""
    print(f"Importing {file_path} -> {table_name}")

    # Load file into DataFrame
    df = pd.read_csv(file_path, sep="\t", dtype=str)
    df = df[columns]  # keep only selected columns
    df = df.fillna("")

    session = get_db()
    for _, row in df.iterrows():
        obj = ONET(
            onet_code=row[columns[0]],
            title=row[columns[1]],
            description=row[columns[2]]
        )
        # Upsert: merge ensures insert or update
        session.merge(obj)
    
    session.commit()
    session.close()
    print
    print(f"Imported {len(df)} rows into {table_name}")

def sync():
    file_path = download_onet()
    import_to_postgres(file_path, "onet", ["O*NET-SOC Code", "Title", "Description"])

    print("Done!")