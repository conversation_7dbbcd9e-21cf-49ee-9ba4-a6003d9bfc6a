from sqlalchemy import Column, String, Text, Integer, ForeignKey
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()

class OnetOccupation(Base):
    __tablename__ = "onet_occupations"

    onet_code = Column(String, primary_key=True, index=True)
    title = Column(Text, nullable=False)
    description = Column(Text)

    soc_xwalks = relationship("OnetSocXwalk", back_populates="onet")


class SocOccupation(Base):
    __tablename__ = "soc_occupations"

    soc_code = Column(String, primary_key=True, index=True)
    title = Column(Text, nullable=False)
    description = Column(Text)

    onet_xwalks = relationship("OnetSocXwalk", back_populates="soc")
    naics_xwalks = relationship("SocNaicsXwalk", back_populates="soc")


class NaicsIndustry(Base):
    __tablename__ = "naics_industries"

    naics_code = Column(String, primary_key=True, index=True)
    title = Column(Text, nullable=False)

    soc_xwalks = relationship("SocNaicsXwalk", back_populates="naics")


class OnetSocXwalk(Base):
    __tablename__ = "onet_soc_xwalk"

    id = Column(Integer, primary_key=True, autoincrement=True)
    onet_code = Column(String, ForeignKey("onet_occupations.onet_code"))
    soc_code = Column(String, ForeignKey("soc_occupations.soc_code"))

    onet = relationship("OnetOccupation", back_populates="soc_xwalks")
    soc = relationship("SocOccupation", back_populates="onet_xwalks")


class SocNaicsXwalk(Base):
    __tablename__ = "soc_naics_xwalk"

    id = Column(Integer, primary_key=True, autoincrement=True)
    soc_code = Column(String, ForeignKey("soc_occupations.soc_code"))
    naics_code = Column(String, ForeignKey("naics_industries.naics_code"))

    soc = relationship("SocOccupation", back_populates="naics_xwalks")
    naics = relationship("NaicsIndustry", back_populates="soc_xwalks")
