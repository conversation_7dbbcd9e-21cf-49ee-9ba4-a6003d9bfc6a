
from sqlalchemy import Column, ForeignKey, String, Text
from sqlalchemy.orm import relationship
from db import Base

class NAICS(Base):
    __tablename__ = "naics"
    naics_code = Column(String(10), primary_key=True, index=True)
    title = Column(Text, nullable=False)
    description = Column(Text)

    soc_rel = relationship("SocNaicsXwalk", back_populates="naics_rel")

class ONET(Base):
    __tablename__ = "onet"
    onet_code = Column(String(20), primary_key=True, index=True)
    title = Column(Text, nullable=False)
    description = Column(Text)

    soc_rel = relationship("OnetSocXwalk", back_populates="onet_rel")

class SOC(Base):
    __tablename__ = "soc"
    soc_code = Column(String(20), primary_key=True, index=True)
    title = Column(Text, nullable=False)
    description = Column(Text)

    onet_rel = relationship("OnetSocXwalk", back_populates="soc_rel")
    naics_rel = relationship("SocNaicsXwalk", back_populates="soc_rel")

class OnetSocXwalk(Base):
    __tablename__ = "onet_soc_xwalk"
    onet_code = Column(String(20), ForeignKey("onet.onet_code"), primary_key=True)
    soc_code = Column(String(20), ForeignKey("soc.soc_code"), primary_key=True)

    onet_rel = relationship("ONET", back_populates="soc_rel")
    soc_rel = relationship("SOC", back_populates="onet_rel")

class SocNaicsXwalk(Base):
    __tablename__ = "soc_naics_xwalk"
    soc_code = Column(String(20), ForeignKey("soc.soc_code"), primary_key=True)
    naics_code = Column(String(10), ForeignKey("naics.naics_code"), primary_key=True)
    relationship = Column(Text)

    soc_rel = relationship("SOC", back_populates="naics_rel")
    naics_rel = relationship("NAICS", back_populates="soc_rel")
